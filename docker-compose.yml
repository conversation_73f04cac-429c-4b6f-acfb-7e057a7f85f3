version: '3.8'

services:
  # Frontend service with nginx
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
    volumes:
      # Mount nginx config for easy updates during development
      - ./docker/nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend service (placeholder - adjust based on your backend)
  backend:
    # Replace with your actual backend image/build
    image: your-backend-image:latest
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    # Add your backend-specific configuration here

networks:
  default:
    name: mojo-muse-network
